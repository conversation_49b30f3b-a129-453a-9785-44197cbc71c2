<head>
    <link rel="dns-prefetch" href="index.html">
    <link rel="dns-prefetch" href="http://zelio-developer.botble.com/">
    <link rel="dns-prefetch" href="http://zelio-writer.botble.com/">
    <link rel="dns-prefetch" href="http://www.w3.org/">
    <link rel="dns-prefetch" href="http://www.facebook.com/">
    <link rel="dns-prefetch" href="http://x.com/">
    <link rel="dns-prefetch" href="http://www.youtube.com/">
    <link rel="dns-prefetch" href="http://www.linkedin.com/">
    <link rel="dns-prefetch" href="http://botble.com/">
    <link rel="dns-prefetch" href="http://google.com/">
    <link rel="dns-prefetch" href="http://www.googletagmanager.com/">
    <link rel="dns-prefetch" href="http://zelio.botble.comcookie-policy/">
    <meta charset=utf-8>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1"
        name=viewport>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Showcasing Creative Designs and Innovative Projects')</title>
    <meta name=description content="@yield('meta_description', 'Discover innovative designs, creative projects, and unique artistic works. Showcasing the expertise and vision behind every creation.')">
    <link rel="canonical" href="index.html">
    <meta name=robots content="index, follow">
    <meta property="og:site_name" content="Showcasing Creative Designs and Innovative Projects">
    <meta property="og:type" content="article">
    <meta property="og:title" content="Showcasing Creative Designs and Innovative Projects">
    <meta property="og:description"
        content="Discover innovative designs, creative projects, and unique artistic works. Showcasing the expertise and vision behind every creation.">
    <meta property="og:url" content="index.html">
    <meta property="og:image" content="storage/main/general/favicon.png">
    <meta name=twitter:title content="Showcasing Creative Designs and Innovative Projects">
    <meta name=twitter:description
        content="Discover innovative designs, creative projects, and unique artistic works. Showcasing the expertise and vision behind every creation.">
    <link rel="icon" type=image/x-icon href="storage/main/general/favicon.png">
    <style>
        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqrbs1mixk2z7s.woff2") }}') format('woff2');
            unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqlbs1mixk2.woff2") }}') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqrbs1mixk2z7s.woff2") }}') format('woff2');
            unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqlbs1mixk2.woff2") }}') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqrbs1mixk2z7s.woff2") }}') format('woff2');
            unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqlbs1mixk2.woff2") }}') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqrbs1mixk2z7s.woff2") }}') format('woff2');
            unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqlbs1mixk2.woff2") }}') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqrbs1mixk2z7s.woff2") }}') format('woff2');
            unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Urbanist';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url('{{ asset("storage/fonts/8c6cca1cdc/surbanistv15l0x-df02ifml4hgcymqlbs1mixk2.woff2") }}') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }
    </style>
    <style>
        :root {
            --primary-font: "Urbanist", sans-serif;
            --secondary-font: "Urbanist", sans-serif;
        }
    </style>
    {{-- Theme CSS Assets from public folder --}}
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('vendor/core/plugins/language/css/language-publicd1f1.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('vendor/core/core/base/libraries/ckeditor/content-styles.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/bootstrap.min.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/swiper-bundle.min.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/aos.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/odometer38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/carouselTicker38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/slick.min38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/vendors/magnific-popup38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/fonts/remixicon/remixicon38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/main38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('themes/zelio/css/theme38bb.css') }}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('vendor/core/plugins/announcement/css/announcement5cce.css') }}">
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Showcasing Creative Designs and Innovative Projects",
        "url": "{{ url('/') }}"
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Showcasing Creative Designs and Innovative Projects",
        "url": "{{ url('/') }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ asset('storage/main/general/favicon.png') }}"
        }
    }
    </script>
    <link href="{{ url('/') }}" hreflang="x-default" rel="alternate">
    <link href="{{ url('/') }}" hreflang="en" rel="alternate">
    <script>window.siteUrl = "{{ url('/') }}";</script>
    <style>
        :root {
            --primary-color: #6e4ef2;
            --gradient-color: #8c71ff;
            --bs-primary-rgb: 110, 78, 242;
        }
    </style>
    <style>
        .page_speed_353846434 {
            max-height: 40px
        }

        .page_speed_142723115 {
            height: 16px;
            width: auto;
        }

        .page_speed_1205099044 {
            display: none
        }

        .page_speed_1189428097 {
            translate: none;
            rotate: none;
            scale: none;
            transform: translate(-925.328px, 0px);
        }

        .page_speed_134625730 {
            visibility: visible;
            animation-name: img-anim-top;
        }

        .page_speed_590282898 {
            background-color: #000;
            color: #fff;
        }

        .page_speed_1747309764 {
            max-width: 1170px;
        }

        .page_speed_1046531075 {
            background-color: #fff;
            color: #000;
            border: 1px solid #fff;
        }

        .page_speed_1003585387 {
            background-color: #000;
            color: #fff;
            border: 1px solid #fff;
        }

        /* Custom Services Section Styles */
        .services-section {
            background: var(--bs-dark);
            color: var(--bs-light);
        }

        .services-section .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--bs-light);
            margin-bottom: 1rem;
        }

        .services-section .section-subtitle {
            font-size: 1.1rem;
            color: var(--bs-light);
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .service-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary-color);
            box-shadow: 0 10px 30px rgba(110, 78, 242, 0.2);
        }

        .service-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--bs-light);
            margin-bottom: 1rem;
        }

        .service-description {
            color: var(--bs-light);
            opacity: 0.8;
            line-height: 1.6;
            margin-bottom: 0;
        }
    </style>

    {{-- Additional page-specific styles --}}
    @stack('styles')
</head>
