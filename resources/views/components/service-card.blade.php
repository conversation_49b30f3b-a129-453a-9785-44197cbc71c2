{{-- Service Card Component --}}
@props([
    'title' => 'Service Title',
    'description' => 'Service description',
    'image' => '',
    'link' => '#',
    'index' => 0
])

<div class="card-custom" data-index="{{ $index }}">
    <div class="card__inner bg-6 px-md-5 py-md-6 px-3 py-4">
        {{-- Card Title and Link --}}
        <div class="card__title d-flex align-items-center mb-md-4 mb-3">
            <a href="{{ $link }}" class="card_title_link">
                <h3 class="fw-semibold mb-2">{{ $title }}</h3>
                <p class="mb-0">{!! $description !!}</p>
            </a>
            <a href="{{ $link }}" class="card-icon border text-dark border-dark icon-shape ms-auto icon-md rounded-circle">
                <i class="ri-arrow-right-up-line"></i>
            </a>
        </div>
        
        {{-- Card Image --}}
        <div class="card__image-container zoom-img position-relative">
            <img src="{{ $image }}" 
                 data-bb-lazy="true" 
                 class="card_image"
                 loading="lazy" 
                 alt="{{ $title }}">
            <a href="{{ $link }}" class="card-image-overlay position-absolute start-0 end-0 w-100 h-100"></a>
        </div>
    </div>
</div>
