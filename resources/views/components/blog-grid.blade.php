{{-- Blog Grid Component --}}
@props(['posts' => [], 'currentPage' => 1, 'totalPages' => 1])

<section class="blog-section py-5">
    <div class="container">
        <div class="row">
            @foreach($posts as $post)
                <x-blog-card 
                    :title="$post['title']"
                    :excerpt="$post['excerpt']"
                    :image="$post['image']"
                    :link="$post['link']"
                    :category="$post['category']"
                    :date="$post['date']"
                    :readTime="$post['readTime']"
                />
            @endforeach
        </div>
        
        {{-- Pagination --}}
        @if($totalPages > 1)
            <div class="row">
                <div class="col-12">
                    <nav aria-label="Blog pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            {{-- Previous Page --}}
                            @if($currentPage > 1)
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ $currentPage - 1 }}">
                                        <i class="ri-arrow-left-line"></i>
                                        Previous
                                    </a>
                                </li>
                            @endif
                            
                            {{-- Page Numbers --}}
                            @for($i = 1; $i <= $totalPages; $i++)
                                <li class="page-item {{ $i === $currentPage ? 'active' : '' }}">
                                    <a class="page-link" href="?page={{ $i }}">{{ $i }}</a>
                                </li>
                            @endfor
                            
                            {{-- Next Page --}}
                            @if($currentPage < $totalPages)
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ $currentPage + 1 }}">
                                        Next
                                        <i class="ri-arrow-right-line"></i>
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </nav>
                </div>
            </div>
        @endif
    </div>
</section>
