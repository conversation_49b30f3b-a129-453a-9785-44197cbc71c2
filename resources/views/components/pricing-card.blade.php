{{-- Pricing Card Component --}}
@props([
    'title' => 'Plan Name',
    'price' => '$99',
    'period' => 'Monthly',
    'features' => [],
    'highlighted' => false,
    'buttonText' => 'Get Started',
    'buttonLink' => '#'
])

<div class="col-lg-6 col-md-6 mb-4">
    <div class="pricing-card {{ $highlighted ? 'highlighted' : '' }} bg-900 rounded-4 p-6 h-100 position-relative">
        @if($highlighted)
            <div class="popular-badge position-absolute top-0 start-50 translate-middle">
                <span class="badge bg-primary">Most Popular</span>
            </div>
        @endif
        
        {{-- Plan Header --}}
        <div class="pricing-header text-center mb-4">
            <h4 class="plan-title mb-3">{{ $title }}</h4>
            <div class="price-container mb-3">
                <span class="price-amount h2 text-primary">{{ $price }}</span>
                <span class="price-period text-300">/{{ $period }}</span>
            </div>
        </div>
        
        {{-- Features List --}}
        <div class="features-list mb-4">
            <ul class="list-unstyled">
                @foreach($features as $feature)
                    <li class="feature-item d-flex align-items-center mb-3">
                        <i class="ri-check-line text-primary me-3"></i>
                        <span>{{ $feature }}</span>
                    </li>
                @endforeach
            </ul>
        </div>
        
        {{-- CTA Button --}}
        <div class="pricing-footer text-center mt-auto">
            <a href="{{ $buttonLink }}" class="btn {{ $highlighted ? 'btn-primary' : 'btn-outline-primary' }} w-100">
                {{ $buttonText }}
            </a>
        </div>
    </div>
</div>
