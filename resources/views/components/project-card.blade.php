{{-- Project Card Component --}}
@props([
    'title' => 'Project Title',
    'description' => 'Project description',
    'image' => '',
    'link' => '#',
    'client' => 'Client Name',
    'date' => 'Date',
    'preview' => '#',
    'categories' => []
])

<div class="col-lg-4 col-md-6 mb-6">
    <div class="project-card bg-900 rounded-4 overflow-hidden h-100">
        {{-- Project Image --}}
        <div class="project-image position-relative">
            <img src="{{ $image }}" 
                 alt="{{ $title }}" 
                 class="w-100 project-img">
            <div class="project-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                <a href="{{ $link }}" class="btn btn-primary btn-sm me-2">View Details</a>
                @if($preview !== '#')
                    <a href="{{ $preview }}" class="btn btn-outline-light btn-sm" target="_blank">Live Preview</a>
                @endif
            </div>
        </div>
        
        {{-- Project Content --}}
        <div class="project-content p-4">
            <h5 class="project-title mb-2">
                <a href="{{ $link }}" class="text-decoration-none">{{ $title }}</a>
            </h5>
            <p class="project-description text-300 mb-3">{{ $description }}</p>
            
            {{-- Project Meta --}}
            <div class="project-meta mb-3">
                <div class="row text-sm">
                    <div class="col-6">
                        <span class="text-400">Client:</span>
                        <span class="text-white">{{ $client }}</span>
                    </div>
                    <div class="col-6">
                        <span class="text-400">Date:</span>
                        <span class="text-white">{{ $date }}</span>
                    </div>
                </div>
            </div>
            
            {{-- Categories --}}
            @if(!empty($categories))
                <div class="project-categories">
                    @foreach($categories as $category)
                        <span class="badge bg-primary-subtle text-primary me-1 mb-1">{{ $category }}</span>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
