@extends('layout.index')

@section('title', 'Pricing')
@section('meta_description', 'Flexible Plans Tailored to Meet Your Unique Needs, Ensuring High-Quality Services Without Breaking the Bank')

@section('content')
    <div class="pb-150 container pt-80">
        <div class="mb-8 text-center">
            <h3 class="ds-3 mt-3 mb-4 text-dark">Pricing</h3>
        </div>
        <div class="ck-content">
            <section class="section-pricing-1">
                <div class="row row-cols-1 justify-content-center mt-8 row-cols-md-3">
                    @foreach($plans as $plan)
                    <div class="col">
                        <div class="card-pricing-1 p-6 rounded-4 h-100 d-flex flex-column">
                            <span class="text-uppercase fs-7">{{ $plan['title'] }}</span><br>
                            <h3 class="ds-3 fw-medium text-primary mb-5">
                                ${{ $plan['price'] }}
                                <span class="text-300 fs-4">/{{ $plan['period'] }}</span>
                            </h3>
                            <ul class="ps-3 border-top border-600 pt-5 mb-auto">
                                @foreach($plan['features'] as $feature)
                                <li>
                                    <p class="text-300">{{ $feature }}</p>
                                </li>
                                @endforeach
                            </ul>
                            <a href="{{ $plan['buttonLink'] ?? 'contact' }}" class="btn btn-gradient mt-5 w-100 justify-content-center">
                                {{ $plan['buttonText'] ?? 'Get Started' }}
                                <i class="ri-arrow-right-up-line"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
            </section>
            
            {{-- FAQ Section --}}
            <section class="section-pricing-1">
                <div class="container">
                    <div class="row mt-8">
                        <div class="mx-md-auto text-center col-lg-6">
                            <h2 class="text-300 mb-8">Common Questions</h2>
                            <div class="accordion">
                                @foreach($faqs as $index => $faq)
                                <div class="mb-3 card border-2 rounded-4">
                                    <div class="card-header p-0 border-0">
                                        <a class="p-3 collapsed text-900 fw-bold d-flex align-items-center"
                                           data-bs-toggle="collapse" href="#faq-{{ $index }}">
                                            <p class="fs-5 mb-0 text-dark text-start me-1">{{ $faq['question'] }}</p>
                                            <span class="ms-auto arrow me-2 icon-shape">
                                                <i class="ri-add-line"></i>
                                            </span>
                                        </a>
                                    </div>
                                    <div id="faq-{{ $index }}" class="collapse" data-bs-parent=".accordion">
                                        <p class="px-4 pt-0 text-start card-body">{{ $faq['answer'] }}</p>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
@endsection


