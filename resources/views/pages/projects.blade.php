@extends('layout.index')

@section('title', 'Projects')
@section('meta_description', 'With expertise in mobile app and web design, I transform ideas into visually stunning and user-friendly interfaces that captivate and retain users. Explore my work and see design in action.')

@section('content')
    {{-- Page Header --}}
    <x-page-header title="Projects" />
    
    {{-- Projects Content --}}
    <div class="ck-content">
        <x-projects-grid :projects="$projects" />
    </div>
@endsection

@push('styles')
<style>
    /* Project Card Styles */
    .project-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .project-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    
    .project-image {
        overflow: hidden;
        height: 250px;
    }
    
    .project-img {
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .project-overlay {
        background: rgba(0, 0, 0, 0.8);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .project-card:hover .project-img {
        transform: scale(1.05);
    }
    
    .project-card:hover .project-overlay {
        opacity: 1;
    }
    
    .project-title a {
        color: var(--bs-light);
        transition: color 0.3s ease;
    }
    
    .project-title a:hover {
        color: var(--primary-color);
    }
    
    .project-meta {
        font-size: 0.875rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1rem;
    }
    
    .project-categories .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .bg-primary-subtle {
        background-color: rgba(110, 78, 242, 0.1) !important;
    }
    
    .text-primary {
        color: var(--primary-color) !important;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .project-image {
            height: 200px;
        }
        
        .project-content {
            padding: 1rem;
        }
    }
</style>
@endpush
