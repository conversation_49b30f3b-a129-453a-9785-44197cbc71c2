@extends('layout.index')

@section('title', 'Contact')
@section('meta_description', 'Get in touch with me for your next project. I\'m always excited to collaborate with innovative minds and bring creative ideas to life.')

@section('content')
    {{-- <PERSON> Header --}}
    <x-page-header title="Contact" />
    
    {{-- Contact Content --}}
    <div class="ck-content">
        {{-- Contact Info Section --}}
        <x-contact-info :contactInfo="$contactInfo" />
        
        {{-- Contact Form Section --}}
        <x-contact-form :formAction="route('contact.store')" />
    </div>
@endsection

@push('styles')
<style>
    /* Contact Item Styles */
    .contact-item {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .contact-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    
    .contact-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(110, 78, 242, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }
    
    .contact-title {
        font-weight: 600;
        color: var(--bs-light);
    }
    
    .contact-details a {
        transition: color 0.3s ease;
    }
    
    .contact-details a:hover {
        color: var(--primary-color) !important;
    }
    
    /* Contact Form Styles */
    .contact-form-wrapper {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .contact-form .form-label {
        color: var(--bs-light);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .contact-form .form-control {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--bs-light);
        padding: 0.75rem 1rem;
        border-radius: 8px;
    }
    
    .contact-form .form-control:focus {
        background-color: rgba(255, 255, 255, 0.08);
        border-color: var(--primary-color);
        color: var(--bs-light);
        box-shadow: 0 0 0 0.2rem rgba(110, 78, 242, 0.25);
    }
    
    .contact-form .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
    
    .contact-form textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }
    
    .contact-form .form-check-input {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .contact-form .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .contact-form .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(110, 78, 242, 0.25);
    }
    
    .contact-form .form-check-label {
        color: var(--bs-light);
    }
    
    .contact-form .btn-primary {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .contact-form .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(110, 78, 242, 0.4);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .contact-form-wrapper {
            padding: 2rem;
        }
        
        .contact-item {
            margin-bottom: 1.5rem;
        }
        
        .contact-icon {
            width: 60px;
            height: 60px;
        }
        
        .contact-icon i {
            font-size: 1.5rem !important;
        }
    }
</style>
@endpush
