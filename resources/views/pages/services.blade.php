@extends('layout.index')

@section('title', 'Services')
@section('meta_description', 'With expertise in mobile app and web design, I transform ideas into visually stunning and user-friendly interfaces that captivate and retain users. Explore my work and see design in action.')

@section('content')
    {{-- Page Header --}}
    <x-page-header title="Services" />
    
    {{-- Services Content --}}
    <div class="ck-content">
        <x-services-grid :services="$services" />
    </div>
@endsection

@push('styles')
<style>
    /* Service Card Styles */
    .card-custom {
        margin-bottom: 2rem;
        transition: transform 0.3s ease;
    }
    
    .card-custom:hover {
        transform: translateY(-5px);
    }
    
    .card__inner {
        border-radius: 12px;
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .card__inner:hover {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .card_title_link {
        text-decoration: none;
        color: inherit;
        flex: 1;
    }
    
    .card_title_link:hover {
        color: var(--primary-color);
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .card-icon:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
    }
    
    .card__image-container {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .card_image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .card-custom:hover .card_image {
        transform: scale(1.05);
    }
    
    .card-image-overlay {
        background: transparent;
        transition: background 0.3s ease;
    }
    
    .card-custom:hover .card-image-overlay {
        background: rgba(110, 78, 242, 0.1);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card__inner {
            padding: 1.5rem 1rem;
        }
        
        .card_image {
            height: 150px;
        }
    }
</style>
@endpush
