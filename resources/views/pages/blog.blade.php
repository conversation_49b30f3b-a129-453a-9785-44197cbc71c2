@extends('layout.index')

@section('title', 'Blog')
@section('meta_description', 'Explore the insights and trends shaping our industry through our blog posts covering web development, design, and technology.')

@section('content')
    {{-- Page Header --}}
    <x-page-header title="Blog" />
    
    {{-- Blog Content --}}
    <div class="ck-content">
        <x-blog-grid :posts="$posts" :currentPage="$currentPage" :totalPages="$totalPages" />
    </div>
@endsection

@push('styles')
<style>
    /* Blog Card Styles */
    .blog-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        flex-direction: column;
    }
    
    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    
    .blog-image {
        overflow: hidden;
        height: 250px;
    }
    
    .blog-img {
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .blog-card:hover .blog-img {
        transform: scale(1.05);
    }
    
    .blog-category .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .blog-title a {
        transition: color 0.3s ease;
    }
    
    .blog-title a:hover {
        color: var(--primary-color) !important;
    }
    
    .blog-meta {
        font-size: 0.875rem;
        color: var(--bs-gray-400);
    }
    
    .blog-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .blog-excerpt {
        line-height: 1.6;
    }
    
    /* Pagination Styles */
    .pagination .page-link {
        background-color: transparent;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--bs-light);
        margin: 0 0.25rem;
        border-radius: 8px;
    }
    
    .pagination .page-link:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }
    
    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }
    
    .pagination .page-link:focus {
        box-shadow: 0 0 0 0.2rem rgba(110, 78, 242, 0.25);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .blog-image {
            height: 200px;
        }
        
        .blog-content {
            padding: 1rem;
        }
        
        .pagination .page-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }
    }
</style>
@endpush
