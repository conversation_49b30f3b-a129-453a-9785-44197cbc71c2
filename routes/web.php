<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('pages.index');
})->name('index');


Route::get('/test', function () {
    return view('pages.test');
})->name('test');

Route::get('/services', function () {
    $services = [
        [
            'title' => 'Web Development',
            'description' => 'Building responsive and modern websites using <br> cutting-edge technologies.',
            'image' => 'storage/main/services/1.png',
            'link' => '/services/web-development'
        ],
        [
            'title' => 'UI/UX Design',
            'description' => 'Creating user-centered designs that provide intuitive <br> and engaging user experiences.',
            'image' => 'storage/main/services/2.png',
            'link' => '/services/uiux-design'
        ],
        [
            'title' => 'SEO Optimization',
            'description' => 'Improving website rankings on search engines to drive <br> more organic traffic.',
            'image' => 'storage/main/services/3.png',
            'link' => '/services/seo-optimization'
        ],
        [
            'title' => 'Content Creation',
            'description' => 'Providing high-quality content tailored to your business <br> needs.',
            'image' => 'storage/main/services/4.png',
            'link' => '/services/content-creation'
        ]
    ];

    return view('pages.services', compact('services'));
})->name('services');




Route::get('/projects', function () {
    $projects = [
        [
            'title' => 'E-Commerce Website',
            'description' => 'Modern e-commerce platform with seamless user experience and advanced features.',
            'image' => 'storage/main/projects/1.png',
            'link' => '/projects/e-commerce-website',
            'client' => 'TechCorp Inc.',
            'date' => 'Jan 2024',
            'preview' => 'https://example.com/e-commerce',
            'categories' => ['Development', 'Design', 'E-commerce']
        ],
        [
            'title' => 'Mobile App Design',
            'description' => 'Intuitive mobile application design with modern UI/UX principles.',
            'image' => 'storage/main/projects/2.png',
            'link' => '/projects/mobile-app-design',
            'client' => 'StartupXYZ',
            'date' => 'Dec 2023',
            'preview' => 'https://example.com/mobile-app',
            'categories' => ['UI/UX', 'Mobile', 'Design']
        ],
        [
            'title' => 'Portfolio Website',
            'description' => 'Creative portfolio website showcasing artistic works and projects.',
            'image' => 'storage/main/projects/3.png',
            'link' => '/projects/portfolio-website',
            'client' => 'Creative Studio',
            'date' => 'Nov 2023',
            'preview' => 'https://example.com/portfolio',
            'categories' => ['Web Development', 'Portfolio', 'Creative']
        ],
        [
            'title' => 'SEO & Marketing Campaign',
            'description' => 'Comprehensive SEO strategy and digital marketing campaign implementation.',
            'image' => 'storage/main/projects/4.png',
            'link' => '/projects/seo-marketing',
            'client' => 'Digital Agency',
            'date' => 'Oct 2023',
            'preview' => '#',
            'categories' => ['SEO', 'Marketing', 'Analytics']
        ]
    ];

    return view('pages.projects', compact('projects'));
})->name('projects');


Route::get('/blog', function () {
    $posts = [
        [
            'title' => 'A Deep Dive into Laravel for Beginners',
            'excerpt' => 'Learn the fundamentals of Laravel framework and build your first web application step by step.',
            'image' => 'storage/main/blog/1.png',
            'link' => '/blog/laravel-for-beginners',
            'category' => 'Web Development',
            'date' => 'Jan 15, 2024',
            'readTime' => '8 min read'
        ],
        [
            'title' => 'Best Practices for Designing User-Friendly Websites',
            'excerpt' => 'Discover essential UX/UI principles that make websites more intuitive and engaging for users.',
            'image' => 'storage/main/blog/2.png',
            'link' => '/blog/user-friendly-design',
            'category' => 'UI/UX Design',
            'date' => 'Jan 10, 2024',
            'readTime' => '6 min read'
        ],
        [
            'title' => '5 Essential Tools for Web Developers in 2024',
            'excerpt' => 'Explore the latest tools and technologies that every web developer should know about.',
            'image' => 'storage/main/blog/3.png',
            'link' => '/blog/web-dev-tools-2024',
            'category' => 'Technology Reviews',
            'date' => 'Jan 5, 2024',
            'readTime' => '5 min read'
        ],
        [
            'title' => 'How to Integrate APIs in Node.js for Your Next Project',
            'excerpt' => 'Step-by-step guide on integrating external APIs in your Node.js applications efficiently.',
            'image' => 'storage/main/blog/4.png',
            'link' => '/blog/nodejs-api-integration',
            'category' => 'Tutorials',
            'date' => 'Dec 28, 2023',
            'readTime' => '7 min read'
        ],
        [
            'title' => 'My Journey in Open Source: 3 Years of Contributions',
            'excerpt' => 'Sharing my experience contributing to open source projects and lessons learned along the way.',
            'image' => 'storage/main/blog/5.png',
            'link' => '/blog/open-source-journey',
            'category' => 'Career Journey',
            'date' => 'Dec 20, 2023',
            'readTime' => '4 min read'
        ],
        [
            'title' => 'Optimizing Web Performance with React.js',
            'excerpt' => 'Learn advanced techniques to optimize your React applications for better performance.',
            'image' => 'storage/main/blog/6.png',
            'link' => '/blog/react-performance',
            'category' => 'Web Development',
            'date' => 'Dec 15, 2023',
            'readTime' => '9 min read'
        ]
    ];
    
    $currentPage = (int) request('page', 1);
    $totalPages = 2; // Simulate pagination
    
    return view('pages.blog', compact('posts', 'currentPage', 'totalPages'));
})->name('blog');

Route::get('/contact', function () {
    $contactInfo = [
        'phone' => '******-567-8901',
        'email' => '<EMAIL>',
        'social' => 'https://twitter.com/username',
        'socialHandle' => '@username',
        'address' => '0811 Erdman Prairie, Joaville CA',
        'mapLink' => 'https://maps.google.com/?q=0811+Erdman+Prairie,+Joaville+CA'
    ];
    
    return view('pages.contact', compact('contactInfo'));
})->name('contact');

Route::post('/contact', function (Illuminate\Http\Request $request) {
    // Handle contact form submission
    $validated = $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email',
        'phone' => 'nullable|string',
        'subject' => 'required|string|max:255',
        'message' => 'required|string',
        'terms' => 'required|accepted'
    ]);
    
    // Here you would typically send an email or save to database
    // For now, just redirect back with success message
    return redirect()->back()->with('success', 'Thank you for your message! I\'ll get back to you soon.');
})->name('contact.store');


Route::get('/pricing', function () {
    $plans = [
        [
            'title' => 'Basic',
            'price' => '$49',
            'period' => 'Hourly',
            'features' => [
                'Wireframe requirement analysis',
                'Professional design tools (Figma, Adobe XD)',
                'Modern development technologies',
                'Real-time collaboration methods',
                'Dedicated availability during project',
                '30 days support after completion'
            ],
            'highlighted' => false,
            'buttonText' => 'Get Started',
            'buttonLink' => route('contact')
        ],
        [
            'title' => 'Business',
            'price' => '$99',
            'period' => 'Hourly',
            'features' => [
                'No wireframe needed - direct design',
                'Priority project handling',
                'Advanced development features',
                'Premium collaboration tools',
                'Extended availability and support',
                '60 days support after completion',
                'Customer care gifts included',
                'Free consultation sessions'
            ],
            'highlighted' => true,
            'buttonText' => 'Get Started',
            'buttonLink' => route('contact')
        ]
    ];
    
    $faqs = [
        [
            'question' => 'How do you collaborate with clients during the consulting process?',
            'answer' => 'I believe in transparent and continuous communication throughout the project. We use modern collaboration tools like Slack, Zoom, and shared project management platforms to ensure you\'re always updated on progress and can provide feedback in real-time.'
        ],
        [
            'question' => 'How long does a typical consulting engagement last?',
            'answer' => 'Project duration varies depending on scope and complexity. Simple websites typically take 2-4 weeks, while complex applications can take 2-3 months. I provide detailed timelines during our initial consultation.'
        ],
        [
            'question' => 'What sets your leadership development programs apart?',
            'answer' => 'My approach combines technical expertise with user-centered design principles. I focus on creating solutions that not only look great but also provide exceptional user experiences and achieve your business goals.'
        ],
        [
            'question' => 'How do you stay updated on industry trends and best practices?',
            'answer' => 'I continuously invest in learning through online courses, attending conferences, participating in developer communities, and contributing to open-source projects. This ensures my services are informed by the latest insights and innovations.'
        ]
    ];
    
    return view('pages.pricing', compact('plans', 'faqs'));
})->name('pricing');

Route::post('/ajax/render-ui-blocks', function (Illuminate\Http\Request $request) {
    $name = $request->input('name');
    $attributes = $request->input('attributes', []);

    // Mock data for different shortcode types
    $mockData = [
        'services' => '<section class="section-service-1 position-relative pt-120 pb-120 overflow-hidden">
            <div class="container position-relative z-1">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">What do I offer?</h2>
                        <p class="fs-5 mb-0">My journey started with a fascination for design and technology, leading me to specialize in UI/UX design</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-8 mx-lg-auto">
                        <div class="card-scroll mt-8">
                            <div class="cards">
                                <div class="card-custom" data-index="0">
                                    <div class="card__inner bg-6 px-md-5 py-md-6 px-3 py-4">
                                        <div class="card__title d-flex align-items-center mb-md-4 mb-3">
                                            <a href="/services" class="card_title_link">
                                                <h3 class="fw-semibold mb-2">Web Development</h3>
                                                <p class="mb-0">Building responsive and modern websites using cutting-edge technologies.</p>
                                            </a>
                                            <a href="/services" class="card-icon border text-dark border-dark icon-shape ms-auto icon-md rounded-circle">
                                                <i class="ri-arrow-right-up-line"></i>
                                            </a>
                                        </div>
                                        <div class="card__image-container zoom-img position-relative">
                                            <img src="storage/main/services/1.png" data-bb-lazy="true" class="card_image" loading="lazy" alt="Web Development">
                                            <a href="/services" class="card-image-overlay position-absolute start-0 end-0 w-100 h-100"></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-custom" data-index="1">
                                    <div class="card__inner bg-6 px-md-5 py-md-6 px-3 py-4">
                                        <div class="card__title d-flex align-items-center mb-md-4 mb-3">
                                            <a href="/services" class="card_title_link">
                                                <h3 class="fw-semibold mb-2">UI/UX Design</h3>
                                                <p class="mb-0">Creating user-centered designs that provide intuitive and engaging user experiences.</p>
                                            </a>
                                            <a href="/services" class="card-icon border text-dark border-dark icon-shape ms-auto icon-md rounded-circle">
                                                <i class="ri-arrow-right-up-line"></i>
                                            </a>
                                        </div>
                                        <div class="card__image-container zoom-img position-relative">
                                            <img src="storage/main/services/2.png" data-bb-lazy="true" class="card_image" loading="lazy" alt="UI/UX Design">
                                            <a href="/services" class="card-image-overlay position-absolute start-0 end-0 w-100 h-100"></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-custom" data-index="2">
                                    <div class="card__inner bg-6 px-md-5 py-md-6 px-3 py-4">
                                        <div class="card__title d-flex align-items-center mb-md-4 mb-3">
                                            <a href="/services" class="card_title_link">
                                                <h3 class="fw-semibold mb-2">SEO Optimization</h3>
                                                <p class="mb-0">Improving website rankings on search engines to drive more organic traffic.</p>
                                            </a>
                                            <a href="/services" class="card-icon border text-dark border-dark icon-shape ms-auto icon-md rounded-circle">
                                                <i class="ri-arrow-right-up-line"></i>
                                            </a>
                                        </div>
                                        <div class="card__image-container zoom-img position-relative">
                                            <img src="storage/main/services/3.png" data-bb-lazy="true" class="card_image" loading="lazy" alt="SEO Optimization">
                                            <a href="/services" class="card-image-overlay position-absolute start-0 end-0 w-100 h-100"></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-custom" data-index="3">
                                    <div class="card__inner bg-6 px-md-5 py-md-6 px-3 py-4">
                                        <div class="card__title d-flex align-items-center mb-md-4 mb-3">
                                            <a href="/services" class="card_title_link">
                                                <h3 class="fw-semibold mb-2">Content Creation</h3>
                                                <p class="mb-0">Providing high-quality content tailored to your business needs.</p>
                                            </a>
                                            <a href="/services" class="card-icon border text-dark border-dark icon-shape ms-auto icon-md rounded-circle">
                                                <i class="ri-arrow-right-up-line"></i>
                                            </a>
                                        </div>
                                        <div class="card__image-container zoom-img position-relative">
                                            <img src="storage/main/services/4.png" data-bb-lazy="true" class="card_image" loading="lazy" alt="Content Creation">
                                            <a href="/services" class="card-image-overlay position-absolute start-0 end-0 w-100 h-100"></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'projects' => '<section class="section-projects pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">My Latest Works</h2>
                        <p class="fs-5 mb-0">I believe that working hard and trying to learn every day will make me improve in satisfying my customers.</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card-project bg-900 rounded-4 overflow-hidden">
                            <div class="card-project-image">
                                <img src="storage/main/projects/1.png" alt="Project 1" class="w-100">
                            </div>
                            <div class="card-project-content p-4">
                                <h5 class="mb-2">E-commerce Platform</h5>
                                <p class="text-300 mb-0">Modern e-commerce solution with seamless user experience</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card-project bg-900 rounded-4 overflow-hidden">
                            <div class="card-project-image">
                                <img src="storage/main/projects/2.png" alt="Project 2" class="w-100">
                            </div>
                            <div class="card-project-content p-4">
                                <h5 class="mb-2">Mobile App Design</h5>
                                <p class="text-300 mb-0">Intuitive mobile application with modern design principles</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card-project bg-900 rounded-4 overflow-hidden">
                            <div class="card-project-image">
                                <img src="storage/main/projects/3.png" alt="Project 3" class="w-100">
                            </div>
                            <div class="card-project-content p-4">
                                <h5 class="mb-2">Brand Identity</h5>
                                <p class="text-300 mb-0">Complete brand identity design for startup company</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'resume' => '<section class="section-resume pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">My Resume</h2>
                        <p class="fs-5 mb-0">I believe that working hard and trying to learn every day will make me improve in satisfying my customers.</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-6">
                        <h4 class="mb-4">Education</h4>
                        <div class="resume-item mb-4">
                            <div class="resume-year">2018 - 2019</div>
                            <h5>Certification in UX Design</h5>
                            <p class="text-300">University of Stanford</p>
                        </div>
                        <div class="resume-item mb-4">
                            <div class="resume-year">2017 - 2018</div>
                            <h5>Certification in Web Dev</h5>
                            <p class="text-300">University of Stanford</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <h4 class="mb-4">Experience</h4>
                        <div class="resume-item mb-4">
                            <div class="resume-year">2019 - Present</div>
                            <h5>Senior UI/UX Designer</h5>
                            <p class="text-300">Leader in Creative team</p>
                        </div>
                        <div class="resume-item mb-4">
                            <div class="resume-year">2016 - 2019</div>
                            <h5>UI/UX Designer</h5>
                            <p class="text-300">Tech Startup</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'skills' => '<section class="section-skills pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">My Skills</h2>
                        <p class="fs-5 mb-0">I thrive on turning complex problems into simple, beautiful solutions that enhance user satisfaction.</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="skill-item text-center">
                            <img src="storage/main/skills/1.png" alt="Figma" class="skill-icon mb-3">
                            <h5>Figma</h5>
                            <div class="skill-progress">
                                <div class="progress-bar" style="width: 92%"></div>
                            </div>
                            <span class="skill-percentage">92%</span>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="skill-item text-center">
                            <img src="storage/main/skills/2.png" alt="Adobe XD" class="skill-icon mb-3">
                            <h5>Adobe XD</h5>
                            <div class="skill-progress">
                                <div class="progress-bar" style="width: 87%"></div>
                            </div>
                            <span class="skill-percentage">87%</span>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="skill-item text-center">
                            <img src="storage/main/skills/3.png" alt="Illustrator" class="skill-icon mb-3">
                            <h5>Illustrator</h5>
                            <div class="skill-progress">
                                <div class="progress-bar" style="width: 82%"></div>
                            </div>
                            <span class="skill-percentage">82%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'image-slider' => '<section class="section-brands pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">Trusted by industry leaders</h2>
                        <p class="fs-5 mb-0">I have collaborated with many large corporations, companies, and agencies around the world</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-2 col-md-3 col-4 mb-4">
                        <div class="brand-logo text-center">
                            <img src="storage/main/brands/1.png" alt="Brave" class="brand-image">
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-4 mb-4">
                        <div class="brand-logo text-center">
                            <img src="storage/main/brands/2.png" alt="Circle" class="brand-image">
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-4 mb-4">
                        <div class="brand-logo text-center">
                            <img src="storage/main/brands/3.png" alt="Discord" class="brand-image">
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'testimonials' => '<section class="section-testimonials pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">Client Testimonials</h2>
                        <p class="fs-5 mb-0">I believe that working hard and trying to learn every day will make me improve in satisfying my customers.</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-6 mb-4">
                        <div class="testimonial-card bg-900 rounded-4 p-6">
                            <p class="mb-4">"Excellent work and professional approach. Highly recommended!"</p>
                            <div class="testimonial-author">
                                <h6>John Doe</h6>
                                <span class="text-300">CEO, Tech Company</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-4">
                        <div class="testimonial-card bg-900 rounded-4 p-6">
                            <p class="mb-4">"Amazing design skills and attention to detail. Great experience!"</p>
                            <div class="testimonial-author">
                                <h6>Jane Smith</h6>
                                <span class="text-300">Marketing Director</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>',
        'blog-posts' => '<section class="section-blog pt-120 pb-120">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="ds-2 mt-3 mb-3">Recent blog</h2>
                        <p class="fs-5 mb-0">Explore the insights and trends shaping our industry</p>
                    </div>
                </div>
                <div class="row mt-8">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="blog-card bg-900 rounded-4 overflow-hidden">
                            <div class="blog-image">
                                <img src="storage/main/blog/1.png" alt="Blog Post 1" class="w-100">
                            </div>
                            <div class="blog-content p-4">
                                <h5 class="mb-2">Design Trends 2024</h5>
                                <p class="text-300 mb-0">Latest design trends and innovations in the industry</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="blog-card bg-900 rounded-4 overflow-hidden">
                            <div class="blog-image">
                                <img src="storage/main/blog/2.png" alt="Blog Post 2" class="w-100">
                            </div>
                            <div class="blog-content p-4">
                                <h5 class="mb-2">UX Best Practices</h5>
                                <p class="text-300 mb-0">Essential UX principles for better user experience</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="blog-card bg-900 rounded-4 overflow-hidden">
                            <div class="blog-image">
                                <img src="storage/main/blog/3.png" alt="Blog Post 3" class="w-100">
                            </div>
                            <div class="blog-content p-4">
                                <h5 class="mb-2">Web Development Tips</h5>
                                <p class="text-300 mb-0">Modern web development techniques and tools</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>'
    ];

    $data = $mockData[$name] ?? '<div class="text-center p-5"><p>Content not found for: ' . $name . '</p></div>';

    return response()->json([
        'error' => false,
        'data' => $data
    ]);
});
